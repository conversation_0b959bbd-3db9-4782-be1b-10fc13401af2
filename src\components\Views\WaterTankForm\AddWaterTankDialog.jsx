import React, { useState, useContext, useEffect } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Typography,
  Grid,
  makeStyles,
  Divider,
} from '@material-ui/core';
import { UserContext } from '../../../context/UserProvider';
import { db } from '../../../config/firebase';

const useStyles = makeStyles((theme) => ({
  formControl: {
    margin: theme.spacing(1),
    minWidth: 200,
  },
  sectionTitle: {
    marginTop: theme.spacing(2),
    marginBottom: theme.spacing(1),
  },
  divider: {
    margin: theme.spacing(2, 0),
  },
}));

const AddWaterTankDialog = ({ open, onClose, onSuccess, editingTank }) => {
  const classes = useStyles();
  const { usuario, currentMac, canIdIrrigation, togglesNames, togglesUid, switchesNames,
    switchesUid,levelSensorsUid, levelSensorsNames } = useContext(UserContext);

  // Estados para los campos del formulario
  const [tankName, setTankName] = useState('');
  const [fillPump, setFillPump] = useState('');
  const [fillingWaterSource, setFillingWaterSource] = useState('');
  const [fillingValves, setFillingValves] = useState([]);
  const [emptyPump, setEmptyPump] = useState('');
  const [emptyingTank, setEmptyingTank] = useState('');
  const [emptyingValves, setEmptyingValves] = useState([]);
  const [minLevelSensor, setMinLevelSensor] = useState('');
  const [maxLevelSensor, setMaxLevelSensor] = useState('');
  const [waterLevelSensor, setWaterLevelSensor] = useState(''); // Sensor global de nivel de agua
  const [recirculationValve, setRecirculationValve] = useState(''); // Electroválvula de recirculación
  const [emptyTankLevelSensor, setEmptyTankLevelSensor] = useState(''); // Sensor de nivel para el tanque de vaciado/trasvase
  // Estados para los tiempos
  const [fillTimeMinutes, setFillTimeMinutes] = useState('');
  const [fillTimeSeconds, setFillTimeSeconds] = useState('');
  const [emptyTimeMinutes, setEmptyTimeMinutes] = useState('');
  const [emptyTimeSeconds, setEmptyTimeSeconds] = useState('');
  const [waitTimeMinutes, setWaitTimeMinutes] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState('');

  // Estado para rastrear todas las selecciones únicas (usado internamente para validación)
  const [selectedUniqueOptions, setSelectedUniqueOptions] = useState({
    pumps: [], // Para bombas de llenado y vaciado
    tanks: [], // Para fuente de agua y tanque de vaciado
    specialValves: [], // Para válvulas especiales como la de recirculación
  });

  // Efecto para cargar los datos del tanque cuando se está editando
  useEffect(() => {
    if (editingTank) {
      // Buscar los nombres correspondientes a los UIDs
      const fillData = editingTank.fillData;
      const emptyData = editingTank.emptyData;

      // Establecer el nombre del tanque
      setTankName(editingTank.name);

      // Establecer el sensor de nivel de agua
      setWaterLevelSensor(editingTank.waterLevelSensor || '');

      // Establecer la electroválvula de recirculación
      const recirculationValveIndex = togglesUid.findIndex(uid => uid === editingTank.recirculationValve);
      if (recirculationValveIndex !== -1) {
        setRecirculationValve(togglesNames[recirculationValveIndex]);
      } else {
        setRecirculationValve('');
      }

      // Configuración de llenado
      if (fillData) {
        // Buscar el nombre de la bomba de llenado
        const fillPumpIndex = togglesUid.findIndex(uid => uid === fillData.pump);
        if (fillPumpIndex !== -1) {
          setFillPump(togglesNames[fillPumpIndex]);
        }

        // Buscar el nombre de la fuente de agua
        if (fillData.waterSource) {
          const waterSourceIndex = togglesUid.findIndex(uid => uid === fillData.waterSource);
          if (waterSourceIndex !== -1) {
            setFillingWaterSource(togglesNames[waterSourceIndex]);
          }
        }

        // Buscar los nombres de las válvulas adicionales de llenado
        if (fillData.valveOutlets && fillData.valveOutlets.length > 0) {
          const valveNames = fillData.valveOutlets.map(uid => {
            const index = togglesUid.findIndex(toggleUid => toggleUid === uid);
            return index !== -1 ? togglesNames[index] : null;
          }).filter(name => name !== null);

          setFillingValves(valveNames);
        }

        // Establecer el sensor de nivel máximo
        setMaxLevelSensor(fillData.maxSwitch || '');

        // Establecer los tiempos de llenado
        setFillTimeMinutes(fillData.minutes || '');
        setFillTimeSeconds(fillData.seconds || '');
      }

      // Configuración de vaciado
      if (emptyData) {
        // Buscar el nombre de la bomba de vaciado
        const emptyPumpIndex = togglesUid.findIndex(uid => uid === emptyData.pump);
        if (emptyPumpIndex !== -1) {
          setEmptyPump(togglesNames[emptyPumpIndex]);
        }

        // Buscar el nombre del tanque receptor
        if (emptyData.targetTank) {
          const targetTankIndex = togglesUid.findIndex(uid => uid === emptyData.targetTank);
          if (targetTankIndex !== -1) {
            setEmptyingTank(togglesNames[targetTankIndex]);
          }
        }

        // Buscar los nombres de las válvulas adicionales de vaciado
        if (emptyData.valveOutlets && emptyData.valveOutlets.length > 0) {
          const valveNames = emptyData.valveOutlets.map(uid => {
            const index = togglesUid.findIndex(toggleUid => toggleUid === uid);
            return index !== -1 ? togglesNames[index] : null;
          }).filter(name => name !== null);

          setEmptyingValves(valveNames);
        }

        // Establecer el sensor de nivel mínimo
        setMinLevelSensor(emptyData.minSwitch || '');

        // Establecer los tiempos de vaciado
        setEmptyTimeMinutes(emptyData.minutes || '');
        setEmptyTimeSeconds(emptyData.seconds || '');
        setWaitTimeMinutes(emptyData.minutesWaiting || '');

        // Establecer el sensor de nivel del tanque de vaciado
        setEmptyTankLevelSensor(emptyData.targetTankWaterLevelS || '');
      }
    }
  }, [editingTank, togglesNames, togglesUid]);

  // Efecto para actualizar las selecciones únicas cuando cambian los valores
  useEffect(() => {
    const pumps = [];
    const tanks = [];
    const specialValves = []; // Para válvulas especiales como la de recirculación

    // Agregar bombas seleccionadas
    if (fillPump) pumps.push(fillPump);
    if (emptyPump && emptyPump !== fillPump) pumps.push(emptyPump);

    // Agregar tanques seleccionados
    if (fillingWaterSource) tanks.push(fillingWaterSource);
    if (emptyingTank && emptyingTank !== fillingWaterSource) tanks.push(emptyingTank);

    // Agregar válvula de recirculación
    if (recirculationValve) specialValves.push(recirculationValve);

    setSelectedUniqueOptions({ pumps, tanks, specialValves });

    // Limpiar las válvulas que ahora son bombas, tanques o válvulas especiales
    if (fillingValves.length > 0) {
      const newFillingValves = fillingValves.filter(name =>
        !(fillPump === name || emptyPump === name ||
          fillingWaterSource === name || emptyingTank === name ||
          recirculationValve === name)
      );

      if (newFillingValves.length !== fillingValves.length) {
        setFillingValves(newFillingValves);
      }
    }

    if (emptyingValves.length > 0) {
      const newEmptyingValves = emptyingValves.filter(name =>
        !(fillPump === name || emptyPump === name ||
          fillingWaterSource === name || emptyingTank === name ||
          recirculationValve === name)
      );

      if (newEmptyingValves.length !== emptyingValves.length) {
        setEmptyingValves(newEmptyingValves);
      }
    }
  }, [fillPump, emptyPump, fillingWaterSource, emptyingTank, fillingValves, emptyingValves, recirculationValve]);

  const handleSubmit = async () => {
    // Validación básica
    if (!tankName.trim()) {
      setError('El nombre del tanque es obligatorio');
      return;
    }

    if (!fillPump || !emptyPump) {
      setError('Debe seleccionar bombas para llenado y vaciado');
      return;
    }

    setIsSubmitting(true);
    setError('');

    try {
      // Obtener el índice de las bombas seleccionadas para obtener sus UIDs
      const fillPumpIndex = togglesNames.findIndex(name => name === fillPump);
      const emptyPumpIndex = togglesNames.findIndex(name => name === emptyPump);

      if (fillPumpIndex === -1 || emptyPumpIndex === -1) {
        throw new Error('No se encontraron las bombas seleccionadas');
      }

      const fillPumpUid = togglesUid[fillPumpIndex];
      const emptyPumpUid = togglesUid[emptyPumpIndex];

      // Obtener UIDs de las válvulas adicionales de llenado
      const fillValvesUids = fillingValves.map(valveName => {
        const index = togglesNames.findIndex(name => name === valveName);
        return index !== -1 ? togglesUid[index] : null;
      }).filter(uid => uid !== null);

      // Obtener UIDs de las válvulas adicionales de vaciado
      const emptyValvesUids = emptyingValves.map(valveName => {
        const index = togglesNames.findIndex(name => name === valveName);
        return index !== -1 ? togglesUid[index] : null;
      }).filter(uid => uid !== null);

      // Obtener UID de la fuente de agua si está seleccionada
      let waterSourceUid = null;
      if (fillingWaterSource) {
        const waterSourceIndex = togglesNames.findIndex(name => name === fillingWaterSource);
        if (waterSourceIndex !== -1) {
          waterSourceUid = togglesUid[waterSourceIndex];
        }
      }

      // Obtener UID del tanque receptor si está seleccionado
      let emptyingTankUid = null;
      if (emptyingTank) {
        const emptyingTankIndex = togglesNames.findIndex(name => name === emptyingTank);
        if (emptyingTankIndex !== -1) {
          emptyingTankUid = togglesUid[emptyingTankIndex];
        }
      }

      // Obtener UID de la electroválvula de recirculación si está seleccionada
      let recirculationValveUid = null;
      if (recirculationValve) {
        const recirculationValveIndex = togglesNames.findIndex(name => name === recirculationValve);
        if (recirculationValveIndex !== -1) {
          recirculationValveUid = togglesUid[recirculationValveIndex];
        }
      }

      // Crear el objeto del tanque (nuevo o actualizado)
      const tankData = {
        name: tankName,
        lastAction: editingTank ? editingTank.action || "fill" : "fill", // Mantener la acción actual o usar "fill" por defecto
        lastExecution: editingTank ? editingTank.lastExecution : "Never", // Mantener la última ejecución o usar "Never"
        levelSensor: waterLevelSensor || null, // Sensor global de nivel de agua
        recirculationValve: recirculationValveUid, // Electroválvula de recirculación
        fill: {
          pump: fillPumpUid,
          maxSwitch: maxLevelSensor || null, // Corregido: maxSwitch debe usar maxLevelSensor
          waterSource: waterSourceUid,
          valveOutlets: fillValvesUids,
          minutes: fillTimeMinutes,
          seconds: fillTimeSeconds,
        },
        empty: {
          pump: emptyPumpUid,
          minSwitch: minLevelSensor || null, // Corregido: minSwitch debe usar minLevelSensor
          targetTank: emptyingTankUid,
          valveOutlets: emptyValvesUids,
          minutes: emptyTimeMinutes,
          seconds: emptyTimeSeconds,
          minutesWaiting: waitTimeMinutes,
          targetTankWaterLevelS: emptyTankLevelSensor || null,
        }
      };

      // Ruta donde se guardarán los datos
      const waterTankAddr = `${usuario.username}/infoDevices/${currentMac}/${canIdIrrigation}/waterTankControl`;
      const waterDocRef = db.collection(waterTankAddr).doc("tankRows");

      // Obtener los tanques actuales
      const snapshot = await waterDocRef.get();
      let allRows = [];

      if (snapshot.exists) {
        const data = snapshot.data();
        allRows = [...data.allRows];
      }

      if (editingTank) {
        // Actualizar el tanque existente
        allRows = allRows.map((row, index) => {
          if (index === editingTank.id) {
            // Mantener la acción actual y la última ejecución
            return {
              ...tankData,
              lastAction: editingTank.action || "fill",
              lastExecution: editingTank.lastExecution || "Never"
            };
          }
          return row;
        });
      } else {
        // Agregar un nuevo tanque
        allRows.push(tankData);
      }

      // Guardar en Firebase
      await waterDocRef.set({ allRows });

      // Limpiar el formulario y cerrar el diálogo
      resetForm();
      onSuccess(allRows, !!editingTank); // Pasar true si estamos editando
    } catch (error) {
      console.error("Error al guardar el tanque:", error);
      setError(`Error al guardar: ${error.message}`);
    } finally {
      setIsSubmitting(false);
    }
  };

  const resetForm = () => {
    setTankName('');
    setFillPump('');
    setFillingWaterSource('');
    setFillingValves([]);
    setEmptyPump('');
    setEmptyingTank('');
    setEmptyingValves([]);
    setMinLevelSensor('');
    setMaxLevelSensor('');
    setWaterLevelSensor(''); // Resetear el sensor global de nivel de agua
    setRecirculationValve(''); // Resetear la electroválvula de recirculación
    setEmptyTankLevelSensor(''); // Resetear el sensor de nivel del tanque de vaciado
    // Resetear los campos de tiempo
    setFillTimeMinutes('');
    setFillTimeSeconds('');
    setEmptyTimeMinutes('');
    setEmptyTimeSeconds('');
    setWaitTimeMinutes('');
    setError('');
    setSelectedUniqueOptions({ pumps: [], tanks: [], specialValves: [] });
  };

  const handleClose = () => {
    resetForm();
    onClose();
  };

  // Función para filtrar opciones disponibles para bombas
  const getAvailablePumpOptions = (currentSelection) => {
    return togglesNames.filter(name => {
      // Si es la selección actual, siempre mostrarla
      if (name === currentSelection) return true;

      // Si ya está seleccionada como bomba, no mostrarla
      if ((fillPump === name && currentSelection !== fillPump) ||
          (emptyPump === name && currentSelection !== emptyPump)) {
        return false;
      }

      // Si ya está seleccionada como tanque, no mostrarla
      if (fillingWaterSource === name || emptyingTank === name) {
        return false;
      }

      // Si ya está seleccionada como válvula de recirculación, no mostrarla
      if (recirculationValve === name) {
        return false;
      }

      return true;
    });
  };

  // Función para filtrar opciones disponibles para tanques
  const getAvailableTankOptions = (currentSelection) => {
    return togglesNames.filter(name => {
      // Si es la selección actual, siempre mostrarla
      if (name === currentSelection) return true;

      // Si ya está seleccionada como tanque, no mostrarla
      if ((fillingWaterSource === name && currentSelection !== fillingWaterSource) ||
          (emptyingTank === name && currentSelection !== emptyingTank)) {
        return false;
      }

      // Si ya está seleccionada como bomba, no mostrarla
      if (fillPump === name || emptyPump === name) {
        return false;
      }

      // Si ya está seleccionada como válvula de recirculación, no mostrarla
      if (recirculationValve === name) {
        return false;
      }

      return true;
    });
  };

  // Función para filtrar opciones disponibles para válvulas
  const getAvailableValveOptions = () => {
    return togglesNames.filter(name => {
      // Si ya está seleccionada como bomba o tanque, no mostrarla
      if (fillPump === name || emptyPump === name ||
          fillingWaterSource === name || emptyingTank === name) {
        return false;
      }

      // Si ya está seleccionada como válvula de recirculación, no mostrarla
      if (recirculationValve === name) {
        return false;
      }

      return true;
    });
  };

  // Función para filtrar opciones disponibles para la válvula de recirculación
  const getAvailableRecirculationValveOptions = (currentSelection) => {
    return togglesNames.filter(name => {
      // Si es la selección actual, siempre mostrarla
      if (name === currentSelection) return true;

      // Si ya está seleccionada como bomba, no mostrarla
      if (fillPump === name || emptyPump === name) {
        return false;
      }

      // Si ya está seleccionada como tanque, no mostrarla
      if (fillingWaterSource === name || emptyingTank === name) {
        return false;
      }

      return true;
    });
  };

  return (
    <Dialog open={open} onClose={handleClose} maxWidth="md" fullWidth>
      <DialogTitle>{editingTank ? 'Editar Tanque de Agua' : 'Agregar Nuevo Tanque de Agua'}</DialogTitle>
      <DialogContent>
        {error && (
          <Typography color="error" variant="body2" gutterBottom>
            {error}
          </Typography>
        )}

        <TextField
          autoFocus
          margin="dense"
          id="name"
          label="Nombre del Tanque"
          type="text"
          fullWidth
          value={tankName}
          onChange={(e) => setTankName(e.target.value)}
          variant="outlined"
        />

        <Typography variant="h6" className={classes.sectionTitle}>
          Sensor de Monitoreo de Nivel
        </Typography>

        <Typography variant="body2" color="textSecondary" gutterBottom>
          Seleccione el sensor que monitorea el nivel de agua en el tanque. Este sensor se utilizará para visualizar el nivel actual del agua.
        </Typography>

        <Grid container spacing={2}>
          <Grid item xs={12} md={6}>
            <FormControl variant="outlined" className={classes.formControl} fullWidth>
              <InputLabel id="water-level-sensor-label">Sensor de Nivel de Agua</InputLabel>
              <Select
                labelId="water-level-sensor-label"
                id="water-level-sensor"
                value={waterLevelSensor}
                onChange={(e) => setWaterLevelSensor(e.target.value)}
                label="Sensor de Nivel de Agua (Monitoreo)"
              >
                <MenuItem value="">
                  <em>Ninguno</em>
                </MenuItem>
                {levelSensorsNames.map((name, index) => (
                  <MenuItem key={`water-level-${index}`} value={levelSensorsUid[index]}>
                    {name}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>
        </Grid>

        <Divider className={classes.divider} />

        <Typography variant="h6" className={classes.sectionTitle}>
          Electroválvula de Recirculación
        </Typography>

        <Typography variant="body2" color="textSecondary" gutterBottom>
          Seleccione la electroválvula de recirculación para el tanque (opcional). Esta válvula es general para el tanque y no corresponde a operaciones de llenado o vaciado.
        </Typography>

        <Grid container spacing={2}>
          <Grid item xs={12} md={6}>
            <FormControl variant="outlined" className={classes.formControl} fullWidth>
              <InputLabel id="recirculation-valve-label">Electroválvula de Recirculación</InputLabel>
              <Select
                labelId="recirculation-valve-label"
                id="recirculation-valve"
                value={recirculationValve}
                onChange={(e) => setRecirculationValve(e.target.value)}
                label="Electroválvula de Recirculación"
              >
                <MenuItem value="">
                  <em>No Aplica</em>
                </MenuItem>
                {getAvailableRecirculationValveOptions(recirculationValve).map((name, index) => (
                  <MenuItem key={`recirculation-valve-${index}`} value={name}>
                    {name}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>
        </Grid>

        <Divider className={classes.divider} />

        <Typography variant="h6" className={classes.sectionTitle}>
          Configuración de Llenado
        </Typography>

        <Grid container spacing={2}>
          <Grid item xs={12} md={6}>
            <FormControl variant="outlined" className={classes.formControl} fullWidth>
              <InputLabel id="fill-pump-label">Bomba de Llenado</InputLabel>
              <Select
                labelId="fill-pump-label"
                id="fill-pump"
                value={fillPump}
                onChange={(e) => setFillPump(e.target.value)}
                label="Bomba de Llenado"
              >
                {getAvailablePumpOptions(fillPump).map((name, index) => (
                  <MenuItem key={`fill-${index}`} value={name}>
                    {name}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>
          <Grid item xs={12} md={6}>
            <FormControl variant="outlined" className={classes.formControl} fullWidth>
              <InputLabel id="fill-waterSource-label">Fuente de agua</InputLabel>
              <Select
                labelId="fill-waterSource-label"
                id="fill-waterSource"
                value={fillingWaterSource}
                onChange={(e) => setFillingWaterSource(e.target.value)}
                label="Fuente de agua"
              >
                {getAvailableTankOptions(fillingWaterSource).map((name, index) => (
                  <MenuItem key={`fill-waterS-${index}`} value={name}>
                    {name}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>

          <Grid item xs={12} md={6}>
            <FormControl variant="outlined" className={classes.formControl} fullWidth>
              <InputLabel id="fill-valves-label">Otras valvulas para llenado</InputLabel>
              <Select
                labelId="fill-extra-valves-label"
                id="extra-fill-valves"
                value={fillingValves}
                onChange={(e) => {
                  // Filtrar cualquier opción que pueda haberse seleccionado como bomba o tanque
                  const filteredValues = e.target.value.filter(name =>
                    !(fillPump === name || emptyPump === name ||
                      fillingWaterSource === name || emptyingTank === name)
                  );
                  setFillingValves(filteredValues);
                }}
                label="Otras valvulas para llenado"
                multiple
                renderValue={(selected) => selected.join(', ')}
              >
                {getAvailableValveOptions().map((name, index) => (
                  <MenuItem key={`fill-valves-${index}`} value={name}>
                    {name}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>

          <Grid item xs={12} md={6}>
            <FormControl variant="outlined" className={classes.formControl} fullWidth>
              <InputLabel id="max-sensor-label">Sensor de Nivel Máximo</InputLabel>
              <Select
                labelId="max-sensor-label"
                id="max-sensor"
                value={maxLevelSensor}
                onChange={(e) => setMaxLevelSensor(e.target.value)}
                label="Sensor de Nivel Máximo"
              >
                {switchesNames.map((name, index) => (
                  <MenuItem key={`max-${index}`} value={switchesUid[index]}>
                    {name}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>

          <Grid item xs={12}>
            <Typography variant="subtitle1" gutterBottom>
              Tiempo Total de Llenado
            </Typography>
          </Grid>

          <Grid item xs={12} md={6}>
            <TextField
              margin="dense"
              id="fill-time-minutes"
              label="Minutos"
              type="number"
              fullWidth
              value={fillTimeMinutes}
              onChange={(e) => { if(e.target.value <= 60 && e.target.value >= 0) setFillTimeMinutes(e.target.value) }}
              variant="outlined"
              InputProps={{ inputProps: { min: 0, max: 60 } }}
            />
          </Grid>

          <Grid item xs={12} md={6}>
            <TextField
              margin="dense"
              id="fill-time-seconds"
              label="Segundos"
              type="number"
              fullWidth
              value={fillTimeSeconds}
              onChange={(e) => { if(e.target.value <= 59 && e.target.value >= 0) setFillTimeSeconds(e.target.value) }}
              variant="outlined"
              InputProps={{ inputProps: { min: 0, max: 59 } }}
            />
          </Grid>
        </Grid>

        <Divider className={classes.divider} />

        <Typography variant="h6" className={classes.sectionTitle}>
          Configuración de Vaciado
        </Typography>

        <Grid container spacing={2}>
          <Grid item xs={12} md={6}>
            <FormControl variant="outlined" className={classes.formControl} fullWidth>
              <InputLabel id="empty-pump-label">Bomba de Vaciado</InputLabel>
              <Select
                labelId="empty-pump-label"
                id="empty-pump"
                value={emptyPump}
                onChange={(e) => setEmptyPump(e.target.value)}
                label="Bomba de Vaciado"
              >
                {getAvailablePumpOptions(emptyPump).map((name, index) => (
                  <MenuItem key={`empty-${index}`} value={name}>
                    {name}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>

          <Grid item xs={12} md={6}>
            <FormControl variant="outlined" className={classes.formControl} fullWidth>
              <InputLabel id="empty-waterSource-label">Tanque receptor/de trasvase(opcional)</InputLabel>
              <Select
                labelId="empty-waterSource-label"
                id="empty-waterSource"
                value={emptyingTank}
                onChange={(e) => setEmptyingTank(e.target.value)}
                label="Tanque receptor/de trasvase(opcional)"
              >
                {getAvailableTankOptions(emptyingTank).map((name, index) => (
                  <MenuItem key={`empty-waterS-${index}`} value={name}>
                    {name}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>

          <Grid item xs={12} md={6}>
            <FormControl variant="outlined" className={classes.formControl} fullWidth>
              <InputLabel id="empty-valves-label">Otras valvulas para vaciado</InputLabel>
              <Select
                labelId="empty-extra-valves-label"
                id="extra-empty-valves"
                value={emptyingValves}
                onChange={(e) => {
                  // Filtrar cualquier opción que pueda haberse seleccionado como bomba o tanque
                  const filteredValues = e.target.value.filter(name =>
                    !(fillPump === name || emptyPump === name ||
                      fillingWaterSource === name || emptyingTank === name)
                  );
                  setEmptyingValves(filteredValues);
                }}
                label="Otras valvulas para vaciado"
                multiple
                renderValue={(selected) => selected.join(', ')}
              >
                {getAvailableValveOptions().map((name, index) => (
                  <MenuItem key={`empty-valves-${index}`} value={name}>
                    {name}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>

          <Grid item xs={12} md={6}>
            <FormControl variant="outlined" className={classes.formControl} fullWidth>
              <InputLabel id="min-sensor-label">Sensor de Nivel Mínimo</InputLabel>
              <Select
                labelId="min-sensor-label"
                id="min-sensor"
                value={minLevelSensor}
                onChange={(e) => setMinLevelSensor(e.target.value)}
                label="Sensor de Nivel Mínimo"
              >
                {switchesNames.map((name, index) => (
                  <MenuItem key={`min-${index}`} value={switchesUid[index]}>
                    {name}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>

          <Grid item xs={12}>
            <Typography variant="subtitle1" gutterBottom>
              Sensor de Nivel para Tanque de Vaciado/Trasvase
            </Typography>
            <Typography variant="body2" color="textSecondary" gutterBottom>
              Seleccione un sensor de nivel para monitorear el tanque de vaciado/trasvase (opcional).
              Este sensor se utilizará para visualizar el nivel de agua en el tanque receptor.
            </Typography>
          </Grid>

          <Grid item xs={12} md={6}>
            <FormControl variant="outlined" className={classes.formControl} fullWidth>
              <InputLabel id="empty-tank-level-sensor-label">Sensor de Nivel para Tanque de Vaciado</InputLabel>
              <Select
                labelId="empty-tank-level-sensor-label"
                id="empty-tank-level-sensor"
                value={emptyTankLevelSensor}
                onChange={(e) => setEmptyTankLevelSensor(e.target.value)}
                label="Sensor de Nivel para Tanque de Vaciado"
              >
                <MenuItem value="">
                  <em>No Aplica</em>
                </MenuItem>
                {levelSensorsNames.map((name, index) => (
                  <MenuItem key={`empty-tank-level-${index}`} value={levelSensorsUid[index]}>
                    {name}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>

          <Grid item xs={12}>
            <Typography variant="subtitle1" gutterBottom>
              Tiempo Total de Vaciado
            </Typography>
          </Grid>

          <Grid item xs={12} md={6}>
            <TextField
              margin="dense"
              id="empty-time-minutes"
              label="Minutos"
              type="number"
              fullWidth
              value={emptyTimeMinutes}
              onChange={(e) => { if(e.target.value <= 60 && e.target.value >= 0) setEmptyTimeMinutes(e.target.value) }}
              variant="outlined"
              InputProps={{ inputProps: { min: 0, max: 60 } }}
            />
          </Grid>

          <Grid item xs={12} md={6}>
            <TextField
              margin="dense"
              id="empty-time-seconds"
              label="Segundos"
              type="number"
              fullWidth
              value={emptyTimeSeconds}
              onChange={(e) => { if(e.target.value <= 59 && e.target.value >= 0) setEmptyTimeSeconds(e.target.value) }}
              variant="outlined"
              InputProps={{ inputProps: { min: 0, max: 59 } }}
            />
          </Grid>

          <Grid item xs={12}>
            <Typography variant="subtitle1" gutterBottom>
              Tiempo de Espera para Vaciado
            </Typography>
          </Grid>

          <Grid item xs={12} md={6}>
            <TextField
              margin="dense"
              id="wait-time-minutes"
              label="Minutos"
              type="number"
              fullWidth
              value={waitTimeMinutes}
              onChange={(e) => { if(e.target.value <= 60 && e.target.value >= 0) setWaitTimeMinutes(e.target.value)}}
              variant="outlined"
              InputProps={{ inputProps: { min: 0 } }}
            />
          </Grid>
        </Grid>
      </DialogContent>
      <DialogActions>
        <Button onClick={handleClose} color="primary" disabled={isSubmitting}>
          Cancelar
        </Button>
        <Button
          onClick={handleSubmit}
          color="primary"
          variant="contained"
          disabled={isSubmitting}
        >
          {isSubmitting ? 'Guardando...' : (editingTank ? 'Actualizar' : 'Guardar')}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default AddWaterTankDialog;
